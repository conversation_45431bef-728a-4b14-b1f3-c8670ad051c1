import React, { useState, useEffect } from 'react';
import { Card, Typography, Spin, message, Empty, Tag, Avatar } from 'antd';
import { CalendarOutlined, BookOutlined } from '@ant-design/icons';
import { MainLayout } from '../layouts';
import { getClasses, type ClassInfo } from '../services/class';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

// 按月分组的课程数据结构
interface MonthlyClasses {
  [monthKey: string]: {
    month: string;
    year: number;
    classes: ClassInfo[];
  };
}

const TrainingCamp: React.FC = () => {
  const [classes, setClasses] = useState<ClassInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [monthlyClasses, setMonthlyClasses] = useState<MonthlyClasses>({});
  const navigate = useNavigate();

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  };

  // 获取月份名称
  const getMonthName = (month: number) => {
    const months = [
      '一月', '二月', '三月', '四月', '五月', '六月',
      '七月', '八月', '九月', '十月', '十一月', '十二月'
    ];
    return months[month - 1];
  };

  // 按月分组课程
  const groupClassesByMonth = (classList: ClassInfo[]) => {
    const grouped: MonthlyClasses = {};
    
    classList.forEach(classItem => {
      const startDate = new Date(classItem.btime);
      const year = startDate.getFullYear();
      const month = startDate.getMonth() + 1;
      const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
      
      if (!grouped[monthKey]) {
        grouped[monthKey] = {
          month: getMonthName(month),
          year,
          classes: []
        };
      }
      
      grouped[monthKey].classes.push(classItem);
    });
    
    return grouped;
  };

  // 获取课程数据
  const fetchClasses = async () => {
    try {
      setLoading(true);
      const classList = await getClasses();
      setClasses(classList);
      const grouped = groupClassesByMonth(classList);
      setMonthlyClasses(grouped);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error('获取课程列表失败:', error);
      
      let errorMessage = '获取课程列表失败，请稍后重试';
      if (error.response) {
        const status = error.response.status;
        if (status === 401) {
          errorMessage = '登录已过期，请重新登录';
        } else if (status === 403) {
          errorMessage = '没有权限访问课程信息';
        } else if (status === 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        }
      } else if (error.request) {
        errorMessage = '网络连接失败，请检查网络设置';
      }
      
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchClasses();
  }, []);

  // 渲染课程卡片
  const renderClassCard = (classItem: ClassInfo) => (
    <Card
      key={classItem.id}
      hoverable
      style={{ marginBottom: '16px', cursor: 'pointer' }}
      onClick={() => navigate(`/class/${classItem.id}/exercises`)}
      cover={
        classItem.pic ? (
          <img
            alt={classItem.name}
            src={classItem.pic}
            style={{ height: '200px', objectFit: 'cover' }}
          />
        ) : (
          <div
            style={{
              height: '200px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Avatar size={64} icon={<BookOutlined />} style={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />
          </div>
        )
      }
      actions={[
        <div key="time" style={{ padding: '0 16px' }}>
          <CalendarOutlined style={{ marginRight: '8px' }} />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {formatDate(classItem.btime)} - {formatDate(classItem.etime)}
          </Text>
        </div>
      ]}
    >
      <Card.Meta
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong style={{ fontSize: '16px' }}>{classItem.name}</Text>
            <Tag color="blue">进行中</Tag>
          </div>
        }
        description={
          <Text type="secondary" style={{ fontSize: '14px' }}>
            {classItem.description || '暂无描述'}
          </Text>
        }
      />
    </Card>
  );

  return (
    <MainLayout>
      <div style={{ maxWidth: 1200, margin: '0 auto' }}>
        <Title level={2}>我的训练营</Title>
        <Text type="secondary" style={{ fontSize: '16px', marginBottom: '24px', display: 'block' }}>
          这里展示您参与的所有课程，按开始时间分月显示
        </Text>

        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>
              <Text type="secondary">正在加载课程信息...</Text>
            </div>
          </div>
        ) : classes.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无课程信息"
            style={{ padding: '50px' }}
          />
        ) : (
          <div>
            {Object.keys(monthlyClasses)
              .sort((a, b) => b.localeCompare(a)) // 按时间倒序排列
              .map(monthKey => {
                const monthData = monthlyClasses[monthKey];
                return (
                  <div key={monthKey} style={{ marginBottom: '40px' }}>
                    <Title level={3} style={{ marginBottom: '20px', color: '#1890ff' }}>
                      {monthData.year}年 {monthData.month}
                      <Text type="secondary" style={{ fontSize: '14px', fontWeight: 'normal', marginLeft: '12px' }}>
                        ({monthData.classes.length} 个课程)
                      </Text>
                    </Title>
                    
                    <div style={{
                      display: 'grid',
                      gridTemplateColumns: 'repeat(auto-fill, minmax(320px, 1fr))',
                      gap: '20px'
                    }}>
                      {monthData.classes.map(renderClassCard)}
                    </div>
                  </div>
                );
              })
            }
          </div>
        )}
      </div>
    </MainLayout>
  );
};

export default TrainingCamp;